import type { ZodError } from 'zod';
import { config } from 'dotenv';
import { expand } from 'dotenv-expand';
import { z } from 'zod';

expand(config());

const EnvSchema = z.object({
	NODE_ENV: z.string().default('development'),
	PORT: z.coerce.number().default(9999),
	LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']),
	DATABASE_URL: z.string().url(),
	// BETTER_AUTH_SECRET: z.string().min(1),
	// BETTER_AUTH_URL: z.string().url(),
});

export type Env = z.infer<typeof EnvSchema>;
// eslint-disable-next-line import/no-mutable-exports
let env: Env;

try {
	env = EnvSchema.parse(process.env);
} catch (e) {
	const error = e as ZodError;
	console.error('❌ Invalid env: ');
	console.error(error.flatten().fieldErrors);
	process.exit(1);
}

export default env;
