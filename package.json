{"name": "task-api", "version": "0.0.1", "description": "A simple task API built with <PERSON><PERSON> and <PERSON><PERSON>", "type": "module", "main": "src/index.ts", "scripts": {"dev": "bun run --hot src/index.ts", "lint": "eslint . --ext .ts", "format": "eslint . --ext .ts --fix", "generate": "prisma generate", "push": "prisma db push"}, "dependencies": {"@hono/zod-openapi": "^0.19.8", "@prisma/client": "^6.9.0", "@scalar/hono-api-reference": "^0.9.1", "better-auth": "^1.2.8", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "hono": "^4.7.10", "hono-pino": "^0.8.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "stoker": "^1.4.2", "zod": "^3.25.42"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@types/bun": "latest", "eslint": "^9.27.0", "prettier": "^3.5.3", "prisma": "^6.9.0", "zod-prisma": "^0.5.4"}}