import configureOpenAPI from '@/lib/configure-open-api';
import createApp from '@/lib/create-app';
import index from '@/routes/index.routes';
import tasks from '@/routes/tasks/tasks.index';
import users from '@/routes/users/users.index';

const app = createApp();

const routes = [index, tasks, users];

configureOpenAPI(app);

routes.forEach(route => app.basePath('/api/').route('/', route));

export default app;
