import configureOpenAPI from '@/lib/configure-open-api';
import createApp from '@/lib/create-app';
import index from '@/routes/index.routes';
import tasks from '@/routes/tasks/tasks.index';
import users from '@/routes/users/users.index';

const app = createApp();

configureOpenAPI(app);

const routes = [index, tasks, users] as const;

routes.forEach(route => {
	app.basePath('/api/v1').route('/', route);
});

export type AppType = (typeof routes)[number];

export default app;
