import { PrismaClient } from '@prisma/client';
import env from '../../env';

// eslint-disable-next-line import/no-mutable-exports
let prisma: PrismaClient;

if (env.NODE_ENV === 'production') {
	prisma = new PrismaClient();
} else {
	const globalWithPrisma = global as typeof globalThis & {
		prisma: PrismaClient;
	};
	if (!globalWithPrisma.prisma) {
		globalWithPrisma.prisma = new PrismaClient();
	}
	prisma = globalWithPrisma.prisma;
}

export default prisma;
