import * as z from 'zod';
import { type CompleteUser, RelatedUserModel } from './index';

export const TaskModel = z.object({
	id: z.string(),
	createdAt: z.date(),
	updatedAt: z.date(),
	title: z.string(),
	content: z.string().nullish(),
	done: z.boolean(),
	authorId: z.string(),
});

export interface CompleteTask extends z.infer<typeof TaskModel> {
	author: CompleteUser;
}

/**
 * RelatedTaskModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedTaskModel: z.ZodSchema<CompleteTask> = z.lazy(() =>
	TaskModel.extend({
		author: RelatedUserModel,
	})
);
