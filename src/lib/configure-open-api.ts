import type { OpenAPIApp } from '@/lib/types';
import pkg from '../../package.json';
import { <PERSON>alar } from '@scalar/hono-api-reference';

export default function configureOpenAPI(app: OpenAPIApp) {
	app.doc('/api/docs', {
		openapi: '3.0.0',
		info: {
			title: 'Task API',
			description: pkg.description,
			version: pkg.version,
		},
	});

	app.get(
		'/api/reference',
		Scalar({
			url: '/api/docs',
			theme: 'kepler',
			layout: 'classic',
			defaultHttpClient: {
				targetKey: 'js',
				clientKey: 'fetch',
			},
		})
	);
}
