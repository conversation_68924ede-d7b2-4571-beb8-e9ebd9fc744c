import {
	OpenAP<PERSON>Hono,
	type RouteConfig,
	type RouteHandler,
} from '@hono/zod-openapi';
import type { PinoLogger } from 'hono-pino';
import { z } from 'zod';
import { UserModel, TaskModel, ProfileModel } from '@/generated/zod';

export interface AppBindings {
	Variables: {
		logger: PinoLogger;
	};
}

export type OpenAPIApp = OpenAPIHono<AppBindings>;

export type AppRouteHandler<R extends RouteConfig> = RouteHandler<
	R,
	AppBindings
>;

// =============================================================================
// USER SCHEMAS
// =============================================================================

// Base User schema for responses (includes profile)
export const UserSchema = UserModel.extend({
	profile: ProfileModel.nullish(),
});

// User schema with tasks for detailed responses
export const UserWithTasksSchema = UserSchema.extend({
	tasks: z.array(
		TaskModel.extend({
			createdAt: z.string(),
			updatedAt: z.string(),
		})
	),
});

// Create User schema (for POST requests)
export const CreateUserSchema = UserModel.pick({
	email: true,
	name: true,
}).extend({
	bio: z.string().optional(), // For profile creation
});

// Update User schema (for PATCH requests)
export const UpdateUserSchema = UserModel.pick({
	email: true,
	name: true,
})
	.partial()
	.extend({
		bio: z.string().optional(), // For profile updates
	});

// =============================================================================
// TASK SCHEMAS
// =============================================================================

// Base Task schema for responses
export const TaskSchema = TaskModel.extend({
	createdAt: z.string(),
	updatedAt: z.string(),
});

// Task schema with author for detailed responses
export const TaskWithAuthorSchema = TaskSchema.extend({
	author: UserModel.pick({
		id: true,
		email: true,
		name: true,
	}),
});

// Create Task schema (for POST requests)
export const CreateTaskSchema = TaskModel.pick({
	title: true,
	content: true,
	authorId: true,
}).extend({
	title: z.string().min(1, 'Title is required'),
	authorId: z.string().min(1, 'Author ID is required'),
});

// Update Task schema (for PATCH requests)
export const UpdateTaskSchema = TaskModel.pick({
	title: true,
	content: true,
	done: true,
})
	.partial()
	.extend({
		title: z.string().min(1, 'Title is required').optional(),
	});
