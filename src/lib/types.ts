import {
	OpenAPIHono,
	type RouteConfig,
	type RouteHandler,
} from '@hono/zod-openapi';
import type { PinoLogger } from 'hono-pino';
import { z } from 'zod';
import { UserModel, TaskModel, ProfileModel } from '@/generated/zod';

export interface AppBindings {
	Variables: { logger: PinoLogger };
}

export type OpenAPIApp = OpenAPIHono<AppBindings>;

export type AppRouteHandler<R extends RouteConfig> = RouteHandler<
	R,
	AppBindings
>;

export const idParamsSchema = z.object({ id: z.string() });

export const UserSchema = UserModel.extend({ profile: ProfileModel.nullish() });

export const UserWithTasksSchema = UserModel.extend({
	tasks: z.array(TaskModel),
	profile: ProfileModel.nullish(),
});

export const CreateUserSchema = UserModel.pick({ email: true, name: true });

export const UpdateUserSchema = CreateUserSchema.partial().extend({
	image: z.string().optional(),
	bio: z.string().optional(),
});

export const TaskSchema = TaskModel;

export const TaskWithAuthorSchema = TaskModel.extend({
	author: UserModel.pick({ id: true, name: true }),
});

export const CreateTaskSchema = TaskModel.omit({
	id: true,
	createdAt: true,
	updatedAt: true,
});

export const UpdateTaskSchema = CreateTaskSchema.partial();
