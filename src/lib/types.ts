import {
	OpenAPIHono,
	type RouteConfig,
	type RouteHandler,
} from '@hono/zod-openapi';
import type { PinoLogger } from 'hono-pino';
import { z } from 'zod';
import {
	UserModel,
	TaskModel,
	ProfileModel,
	RelatedUserModel,
	RelatedTaskModel,
} from '@/generated/zod';

export interface AppBindings {
	Variables: {
		logger: PinoLogger;
	};
}

export type OpenAPIApp = OpenAPIHono<AppBindings>;

export type AppRouteHandler<R extends RouteConfig> = RouteHandler<
	R,
	AppBindings
>;

export const UserSchema = RelatedUserModel;

export const CreateUserSchema = UserModel.pick({
	email: true,
	name: true,
}).extend({ bio: z.string().optional() });

export const UpdateUserSchema = UserModel.pick({ email: true, name: true })
	.partial()
	.extend({ bio: z.string().optional() });

export const TaskSchema = TaskModel;

export const TaskWithAuthorSchema = RelatedTaskModel;

export const CreateTaskSchema = TaskModel.pick({
	title: true,
	content: true,
	authorId: true,
});

export const UpdateTaskSchema = TaskModel.pick({
	title: true,
	content: true,
	done: true,
}).partial();
