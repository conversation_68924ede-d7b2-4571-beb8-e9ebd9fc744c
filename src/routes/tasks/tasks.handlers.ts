import type { App<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/types';
import type {
	ListRoute,
	GetByIdRoute,
	CreateRoute,
	UpdateRoute,
	RemoveRoute,
} from './tasks.routes';
import prisma from '@/db';
import * as HttpStatusCodes from 'stoker/http-status-codes';

export const list: AppRouteHandler<ListRoute> = async c => {
	try {
		const tasks = await prisma.task.findMany({
			orderBy: { createdAt: 'desc' },
		});
		return c.json(tasks, HttpStatusCodes.OK);
	} catch (error) {
		c.var.logger.error('Error fetching tasks:', error);
		return c.json(
			{ message: 'Internal server error' },
			HttpStatusCodes.INTERNAL_SERVER_ERROR
		);
	}
};

export const getById: AppRouteHandler<GetByIdRoute> = async c => {
	try {
		const { id } = c.req.valid('param');
		const task = await prisma.task.findUnique({
			where: { id },
			include: {
				author: {
					select: {
						id: true,
						email: true,
						name: true,
						profile: true,
					},
				},
			},
		});

		if (!task) {
			return c.json({ message: 'Task not found' }, HttpStatusCodes.NOT_FOUND);
		}

		return c.json(task, HttpStatusCodes.OK);
	} catch (error) {
		c.var.logger.error('Error fetching task:', error);
		return c.json(
			{ message: 'Internal server error' },
			HttpStatusCodes.INTERNAL_SERVER_ERROR
		);
	}
};

export const create: AppRouteHandler<CreateRoute> = async c => {
	try {
		const { title, content, authorId } = c.req.valid('json');
		const author = await prisma.user.findUnique({
			where: { id: authorId },
		});

		if (!author) {
			return c.json({ message: 'Author not found' }, HttpStatusCodes.NOT_FOUND);
		}

		const task = await prisma.task.create({
			data: {
				title,
				content: content || null,
				authorId,
			},
			include: {
				author: {
					select: {
						id: true,
						email: true,
						name: true,
					},
				},
			},
		});

		return c.json(task, HttpStatusCodes.CREATED);
	} catch (error) {
		c.var.logger.error('Error creating task:', error);
		return c.json(
			{ message: 'Internal server error' },
			HttpStatusCodes.INTERNAL_SERVER_ERROR
		);
	}
};

export const update: AppRouteHandler<UpdateRoute> = async c => {
	try {
		const { id } = c.req.valid('param');
		const { title, content, done } = c.req.valid('json');
		const existingTask = await prisma.task.findUnique({
			where: { id },
		});

		if (!existingTask) {
			return c.json({ message: 'Task not found' }, HttpStatusCodes.NOT_FOUND);
		}

		const task = await prisma.task.update({
			where: { id },
			data: { title, content, done },
			include: {
				author: {
					select: {
						id: true,
						email: true,
						name: true,
					},
				},
			},
		});

		return c.json(task, HttpStatusCodes.OK);
	} catch (error) {
		c.var.logger.error('Error updating task:', error);
		return c.json(
			{ message: 'Internal server error' },
			HttpStatusCodes.INTERNAL_SERVER_ERROR
		);
	}
};

export const remove: AppRouteHandler<RemoveRoute> = async c => {
	try {
		const { id } = c.req.valid('param');
		const existingTask = await prisma.task.findUnique({
			where: { id },
		});

		if (!existingTask) {
			return c.json({ message: 'Task not found' }, HttpStatusCodes.NOT_FOUND);
		}

		await prisma.task.delete({
			where: { id },
		});

		return c.json({ message: 'Task deleted successfully' }, HttpStatusCodes.OK);
	} catch (error) {
		c.var.logger.error('Error deleting task:', error);
		return c.json(
			{ message: 'Internal server error' },
			HttpStatusCodes.INTERNAL_SERVER_ERROR
		);
	}
};
