import type { <PERSON>pp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/types';
import type {
	ListRoute,
	GetOneRoute,
	CreateRoute,
	UpdateRoute,
	RemoveRoute,
} from './tasks.routes';
import prisma from '@/db';
import * as StatusCodes from 'stoker/http-status-codes';
import * as StatusPhrases from 'stoker/http-status-phrases';

export const list: AppRouteHandler<ListRoute> = async c => {
	const tasks = await prisma.task.findMany({
		orderBy: { createdAt: 'desc' },
	});

	return c.json(tasks, StatusCodes.OK);
};

export const create: AppRouteHandler<CreateRoute> = async c => {
	const { title, content, authorId } = c.req.valid('json');

	const author = await prisma.user.findUnique({
		where: { id: authorId },
	});

	if (!author) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	const task = await prisma.task.create({
		data: { title, content, authorId },
		include: { author: { select: { id: true, name: true } } },
	});

	return c.json(task, StatusCodes.CREATED);
};

export const getOne: AppRouteHandler<GetOneRoute> = async c => {
	const { id } = c.req.valid('param');
	const task = await prisma.task.findUnique({
		where: { id },
		include: { author: true },
	});

	if (!task) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	return c.json(task, StatusCodes.OK);
};

export const update: AppRouteHandler<UpdateRoute> = async c => {
	const { id } = c.req.valid('param');
	const updates = c.req.valid('json');
	const existingTask = await prisma.task.findUnique({ where: { id } });

	if (!existingTask) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	const task = await prisma.task.update({
		where: { id },
		data: updates,
		include: { author: true },
	});

	return c.json(task, StatusCodes.OK);
};

export const remove: AppRouteHandler<RemoveRoute> = async c => {
	const { id } = c.req.valid('param');
	const existingTask = await prisma.task.findUnique({ where: { id } });

	if (!existingTask) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	await prisma.task.delete({ where: { id } });

	return c.body(null, StatusCodes.NO_CONTENT);
};
