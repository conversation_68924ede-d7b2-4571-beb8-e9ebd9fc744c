import { createRout<PERSON>, z } from '@hono/zod-openapi';
import { createRouter } from '@/lib/create-app';
import { jsonContent } from 'stoker/openapi/helpers';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import * as handlers from './tasks.handlers';
import * as routes from './tasks.routes';

const router = createRouter().openapi(routes.list, handlers.list);

export default router;
