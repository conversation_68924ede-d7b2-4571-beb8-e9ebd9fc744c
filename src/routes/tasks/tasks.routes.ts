import { createErrorSchema } from 'stoker/openapi/schemas';
import { createRoute, z } from '@hono/zod-openapi';
import {
	jsonContent,
	jsonContentOneOf,
	jsonContentRequired,
} from 'stoker/openapi/helpers';
import * as StatusCodes from 'stoker/http-status-codes';
import {
	TaskSchema,
	TaskWithAuthorSchema,
	CreateTaskSchema,
	UpdateTaskSchema,
	idParamsSchema,
} from '@/lib/types';
import { notFoundSchema } from '@/lib/constants';

const tags = ['Tasks'];

export const list = createRoute({
	method: 'get',
	path: '/tasks',
	tags,
	responses: {
		[StatusCodes.OK]: jsonContent(z.array(TaskSchema), 'The list of tasks'),
	},
});

export const create = createRoute({
	method: 'post',
	path: '/tasks',
	tags,
	request: {
		body: jsonContentRequired(CreateTaskSchema, 'Task to create'),
	},
	responses: {
		[StatusCodes.CREATED]: jsonContent(
			TaskWithAuthorSchema,
			'The created task'
		),
		[StatusCodes.NOT_FOUND]: jsonContent(notFoundSchema, 'Author not found'),
		[StatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
			createErrorSchema(CreateTaskSchema),
			'The validation error(s)'
		),
	},
});

export const getOne = createRoute({
	method: 'get',
	path: '/tasks/{id}',
	tags,
	request: {
		params: idParamsSchema,
	},
	responses: {
		[StatusCodes.OK]: jsonContent(TaskWithAuthorSchema, 'The task details'),
		[StatusCodes.NOT_FOUND]: jsonContent(notFoundSchema, 'Task not found!'),
		[StatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
			createErrorSchema(idParamsSchema),
			'Invalid id error'
		),
	},
});

export const update = createRoute({
	method: 'patch',
	path: '/tasks/{id}',
	tags,
	request: {
		params: idParamsSchema,
		body: jsonContentRequired(UpdateTaskSchema, 'The task updates'),
	},
	responses: {
		[StatusCodes.OK]: jsonContent(TaskWithAuthorSchema, 'The updated task'),
		[StatusCodes.NOT_FOUND]: jsonContent(notFoundSchema, 'Task not found'),
		[StatusCodes.UNPROCESSABLE_ENTITY]: jsonContentOneOf(
			[createErrorSchema(UpdateTaskSchema), createErrorSchema(idParamsSchema)],
			'Invalid id error or invalid body error'
		),
	},
});

export const remove = createRoute({
	method: 'delete',
	path: '/tasks/{id}',
	tags,
	request: {
		params: idParamsSchema,
	},
	responses: {
		[StatusCodes.NO_CONTENT]: { description: 'Task deleted' },
		[StatusCodes.NOT_FOUND]: jsonContent(notFoundSchema, 'Task not found'),
		[StatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
			createErrorSchema(idParamsSchema),
			'Invalid id error'
		),
	},
});

// Handler types
export type ListRoute = typeof list;
export type GetOneRoute = typeof getOne;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
