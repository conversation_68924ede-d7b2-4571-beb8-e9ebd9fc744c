import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { createRoute, z } from '@hono/zod-openapi';
import { jsonContent } from 'stoker/openapi/helpers';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import { TaskModel } from '@/generated/zod';

const tags = ['Tasks'];

// Routes
export const list = createRoute({
	path: '/tasks',
	method: 'get',
	tags,
	responses: {
		[HttpStatusCodes.OK]: jsonContent(z.array(TaskModel), 'The list of tasks'),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const getById = createRoute({
	tags: ['Tasks'],
	method: 'get',
	path: '/tasks/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(TaskModel, 'The task details'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const create = createRoute({
	tags: ['Tasks'],
	method: 'post',
	path: '/tasks',
	request: {
		body: jsonContent(TaskModel, 'Task data'),
	},
	responses: {
		[HttpStatusCodes.CREATED]: jsonContent(
			TaskModel,
			'Task created successfully'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid task data'),
			'Invalid task data'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Author not found'),
			'Author not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const update = createRoute({
	tags: ['Tasks'],
	method: 'patch',
	path: '/tasks/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
		body: jsonContent(TaskModel, 'Task update data'),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(TaskModel, 'Task updated successfully'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid task data'),
			'Invalid task data'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const remove = createRoute({
	tags: ['Tasks'],
	method: 'delete',
	path: '/tasks/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			createMessageObjectSchema('Task deleted successfully'),
			'Task deleted successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

// Handler types
export type ListRoute = typeof list;
export type GetByIdRoute = typeof getById;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
