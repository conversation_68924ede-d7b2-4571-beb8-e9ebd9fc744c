import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { createRoute, z } from '@hono/zod-openapi';
import { jsonContent } from 'stoker/openapi/helpers';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import {
	TaskSchema,
	TaskWithAuthorSchema,
	CreateTaskSchema,
	UpdateTaskSchema,
} from '@/lib/types';

const tags = ['Tasks'];

export const list = createRoute({
	method: 'get',
	path: '/tasks',
	tags,
	responses: {
		[HttpStatusCodes.OK]: jsonContent(z.array(TaskSchema), 'The list of tasks'),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const getById = createRoute({
	method: 'get',
	path: '/tasks/{id}',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(TaskWithAuthorSchema, 'The task details'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const create = createRoute({
	method: 'post',
	path: '/tasks',
	tags,
	request: {
		body: jsonContent(CreateTaskSchema, 'Task data'),
	},
	responses: {
		[HttpStatusCodes.CREATED]: jsonContent(
			TaskWithAuthorSchema,
			'Task created successfully'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid task data'),
			'Invalid task data'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Author not found'),
			'Author not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const update = createRoute({
	method: 'patch',
	path: '/tasks/{id}',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
		body: jsonContent(UpdateTaskSchema, 'Task update data'),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			TaskWithAuthorSchema,
			'Task updated successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid task data'),
			'Invalid task data'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const remove = createRoute({
	method: 'delete',
	path: '/tasks/{id}',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			createMessageObjectSchema('Task deleted successfully'),
			'Task deleted successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

// Handler types
export type ListRoute = typeof list;
export type GetByIdRoute = typeof getById;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
