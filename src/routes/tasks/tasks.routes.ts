import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { createRoute, z } from '@hono/zod-openapi';
import { jsonContent } from 'stoker/openapi/helpers';
import * as HttpStatusCodes from 'stoker/http-status-codes';

const TaskSchema = z.object({
	id: z.string(),
	title: z.string(),
	content: z.string().nullable(),
	done: z.boolean(),
	createdAt: z.string(),
	updatedAt: z.string(),
	authorId: z.string(),
	author: z.object({
		id: z.string(),
		email: z.string(),
		name: z.string().nullable(),
	}),
});

// Task schema without author for basic listing
const NoAuthorTaskSchema = TaskSchema.omit({ author: true, authorId: true });

const CreateTaskSchema = z.object({
	title: z.string().min(1, 'Title is required'),
	content: z.string().optional(),
	authorId: z.string().min(1, 'Author ID is required'),
});

const UpdateTaskSchema = z
	.object({
		...CreateTaskSchema.shape,
		done: z.boolean().optional(),
	})
	.partial();

export const list = createRoute({
	tags: ['Tasks'],
	method: 'get',
	path: '/tasks',
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			z.array(NoAuthorTaskSchema),
			'The list of tasks'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const getById = createRoute({
	tags: ['Tasks'],
	method: 'get',
	path: '/tasks/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(TaskSchema, 'The task details'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

// Create task route
export const create = createRoute({
	tags: ['Tasks'],
	method: 'post',
	path: '/tasks',
	request: {
		body: jsonContent(CreateTaskSchema, 'Task data'),
	},
	responses: {
		[HttpStatusCodes.CREATED]: jsonContent(
			TaskSchema,
			'Task created successfully'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid task data'),
			'Invalid task data'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Author not found'),
			'Author not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

// Update task route
export const update = createRoute({
	tags: ['Tasks'],
	method: 'patch',
	path: '/tasks/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
		body: jsonContent(UpdateTaskSchema, 'Task update data'),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(TaskSchema, 'Task updated successfully'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid task data'),
			'Invalid task data'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const remove = createRoute({
	tags: ['Tasks'],
	method: 'delete',
	path: '/tasks/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			createMessageObjectSchema('Task deleted successfully'),
			'Task deleted successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('Task not found'),
			'Task not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

// Export types for handlers
export type ListRoute = typeof list;
export type GetByIdRoute = typeof getById;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
