import { createRoute, z } from '@hono/zod-openapi';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { jsonContent } from 'stoker/openapi/helpers';

export const list = createRoute({
	tags: ['Tasks'],
	method: 'get',
	path: '/tasks',
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			z.array(
				z.object({
					id: z.string(),
					name: z.string(),
					done: z.boolean(),
				})
			),
			'The list of tasks'
		),
	},
});

export type ListRoute = typeof list;
