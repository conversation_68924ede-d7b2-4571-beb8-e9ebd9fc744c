import type { App<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/types';
import type { ListRoute, GetByIdRoute, CreateRoute, UpdateRoute, RemoveRoute } from './users.routes';
import prisma from '@/db';
import * as HttpStatusCodes from 'stoker/http-status-codes';

// List all users with their profiles
export const list: AppRouteHandler<ListRoute> = async (c) => {
	try {
		const users = await prisma.user.findMany({
			include: {
				profile: true,
			},
		});
		return c.json(users, HttpStatusCodes.OK);
	} catch (error) {
		c.get('logger').error('Error fetching users:', error);
		return c.json({ message: 'Internal server error' }, HttpStatusCodes.INTERNAL_SERVER_ERROR);
	}
};

// Get user by ID with profile and tasks
export const getById: AppRouteHandler<GetByIdRoute> = async (c) => {
	try {
		const { id } = c.req.valid('param');
		
		const user = await prisma.user.findUnique({
			where: { id },
			include: {
				profile: true,
				tasks: {
					orderBy: { createdAt: 'desc' },
				},
			},
		});

		if (!user) {
			return c.json({ message: 'User not found' }, HttpStatusCodes.NOT_FOUND);
		}

		return c.json(user, HttpStatusCodes.OK);
	} catch (error) {
		c.get('logger').error('Error fetching user:', error);
		return c.json({ message: 'Internal server error' }, HttpStatusCodes.INTERNAL_SERVER_ERROR);
	}
};

// Create a new user and automatically create their profile
export const create: AppRouteHandler<CreateRoute> = async (c) => {
	try {
		const { email, name, bio } = c.req.valid('json');

		// Check if user with email already exists
		const existingUser = await prisma.user.findUnique({
			where: { email },
		});

		if (existingUser) {
			return c.json({ message: 'Email already exists' }, HttpStatusCodes.CONFLICT);
		}

		// Create user and profile in a transaction
		const user = await prisma.user.create({
			data: {
				email,
				name: name || null,
				profile: {
					create: {
						bio: bio || null,
					},
				},
			},
			include: {
				profile: true,
			},
		});

		return c.json(user, HttpStatusCodes.CREATED);
	} catch (error) {
		c.get('logger').error('Error creating user:', error);
		return c.json({ message: 'Internal server error' }, HttpStatusCodes.INTERNAL_SERVER_ERROR);
	}
};

// Update user and optionally their profile
export const update: AppRouteHandler<UpdateRoute> = async (c) => {
	try {
		const { id } = c.req.valid('param');
		const { email, name, bio } = c.req.valid('json');

		// Check if user exists
		const existingUser = await prisma.user.findUnique({
			where: { id },
			include: { profile: true },
		});

		if (!existingUser) {
			return c.json({ message: 'User not found' }, HttpStatusCodes.NOT_FOUND);
		}

		// Check if email is being changed and if it conflicts with another user
		if (email && email !== existingUser.email) {
			const emailConflict = await prisma.user.findUnique({
				where: { email },
			});

			if (emailConflict) {
				return c.json({ message: 'Email already exists' }, HttpStatusCodes.CONFLICT);
			}
		}

		// Prepare update data
		const updateData: any = {};
		if (email !== undefined) updateData.email = email;
		if (name !== undefined) updateData.name = name;

		// Update user and profile
		const user = await prisma.user.update({
			where: { id },
			data: {
				...updateData,
				...(bio !== undefined && {
					profile: {
						update: {
							bio,
						},
					},
				}),
			},
			include: {
				profile: true,
			},
		});

		return c.json(user, HttpStatusCodes.OK);
	} catch (error) {
		c.get('logger').error('Error updating user:', error);
		return c.json({ message: 'Internal server error' }, HttpStatusCodes.INTERNAL_SERVER_ERROR);
	}
};

// Delete user (cascades to profile and tasks due to schema constraints)
export const remove: AppRouteHandler<RemoveRoute> = async (c) => {
	try {
		const { id } = c.req.valid('param');

		// Check if user exists
		const existingUser = await prisma.user.findUnique({
			where: { id },
		});

		if (!existingUser) {
			return c.json({ message: 'User not found' }, HttpStatusCodes.NOT_FOUND);
		}

		// Delete user (profile and tasks will be deleted due to cascade)
		await prisma.user.delete({
			where: { id },
		});

		return c.json({ message: 'User deleted successfully' }, HttpStatusCodes.OK);
	} catch (error) {
		c.get('logger').error('Error deleting user:', error);
		return c.json({ message: 'Internal server error' }, HttpStatusCodes.INTERNAL_SERVER_ERROR);
	}
};
