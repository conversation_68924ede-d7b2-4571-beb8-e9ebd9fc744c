import type { App<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from '@/lib/types';
import type {
	ListRoute,
	GetOneRoute,
	CreateRoute,
	UpdateRoute,
	RemoveRoute,
} from './users.routes';
import prisma from '@/db';
import * as StatusCodes from 'stoker/http-status-codes';
import * as StatusPhrases from 'stoker/http-status-phrases';

export const list: AppRouteHandler<ListRoute> = async c => {
	const users = await prisma.user.findMany({ include: { profile: true } });
	return c.json(users, StatusCodes.OK);
};

export const create: AppRouteHandler<CreateRoute> = async c => {
	const { email, name } = c.req.valid('json');

	const user = await prisma.user.create({
		data: { email, name, profile: { create: { bio: 'New user' } } },
		include: { profile: true },
	});

	return c.json(user, StatusCodes.CREATED);
};

export const getOne: AppRouteHandler<GetOneRoute> = async c => {
	const { id } = c.req.valid('param');

	const user = await prisma.user.findUnique({
		where: { id },
		include: { profile: true, tasks: { orderBy: { createdAt: 'desc' } } },
	});

	if (!user) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	return c.json(user, StatusCodes.OK);
};

export const update: AppRouteHandler<UpdateRoute> = async c => {
	const { id } = c.req.valid('param');
	const { email, name, image, bio } = c.req.valid('json');

	const existingUser = await prisma.user.findUnique({ where: { id } });

	if (!existingUser) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	const user = await prisma.user.update({
		where: { id },
		data: {
			email,
			name,
			profile: { update: { image, bio } },
		},
		include: { profile: true },
	});

	return c.json(user, StatusCodes.OK);
};

export const remove: AppRouteHandler<RemoveRoute> = async c => {
	const { id } = c.req.valid('param');
	const existingUser = await prisma.user.findUnique({ where: { id } });

	if (!existingUser) {
		return c.json({ message: StatusPhrases.NOT_FOUND }, StatusCodes.NOT_FOUND);
	}

	await prisma.user.delete({ where: { id } });

	return c.body(null, StatusCodes.NO_CONTENT);
};
