import { createErrorSchema } from 'stoker/openapi/schemas';
import { createRoute, z } from '@hono/zod-openapi';
import {
	jsonContent,
	jsonContentOneOf,
	jsonContentRequired,
} from 'stoker/openapi/helpers';
import * as StatusCode from 'stoker/http-status-codes';
import {
	UserSchema,
	UserWithTasksSchema,
	CreateUserSchema,
	UpdateUserSchema,
	idParamsSchema,
} from '@/lib/types';
import { notFoundSchema } from '@/lib/constants';
import { userSchema } from 'better-auth/db';

const tags = ['Users'];

export const list = createRoute({
	method: 'get',
	path: '/users',
	tags,
	responses: {
		[StatusCode.OK]: jsonContent(z.array(UserSchema), 'The list of users'),
	},
});

export const create = createRoute({
	method: 'post',
	path: '/users',
	tags,
	request: {
		body: jsonContentRequired(CreateUserSchema, 'The user to be created'),
	},
	responses: {
		[StatusCode.CREATED]: jsonContent(UserSchema, 'The created user'),
		[StatusCode.UNPROCESSABLE_ENTITY]: jsonContent(
			createErrorSchema(CreateUserSchema),
			'The validation error(s)'
		),
	},
});

export const getOne = createRoute({
	path: '/users/{id}',
	method: 'get',
	tags,
	request: {
		params: idParamsSchema,
	},
	responses: {
		[StatusCode.OK]: jsonContent(UserWithTasksSchema, 'The user details'),
		[StatusCode.NOT_FOUND]: jsonContent(notFoundSchema, 'User not found'),
		[StatusCode.UNPROCESSABLE_ENTITY]: jsonContent(
			createErrorSchema(idParamsSchema),
			'Invalid id error'
		),
	},
});

export const update = createRoute({
	method: 'patch',
	path: '/users/{id}',
	tags,
	request: {
		params: idParamsSchema,
		body: jsonContentRequired(UpdateUserSchema, 'The user updates'),
	},
	responses: {
		[StatusCode.OK]: jsonContent(UserSchema, 'The updated user'),
		[StatusCode.NOT_FOUND]: jsonContent(notFoundSchema, 'User not found'),
		[StatusCode.UNPROCESSABLE_ENTITY]: jsonContentOneOf(
			[createErrorSchema(UpdateUserSchema), createErrorSchema(idParamsSchema)],
			'Invalid id error or invalid body error'
		),
	},
});

export const remove = createRoute({
	method: 'delete',
	path: '/users/{id}',
	tags,
	request: {
		params: idParamsSchema,
	},
	responses: {
		[StatusCode.NO_CONTENT]: { description: 'User deleted' },
		[StatusCode.NOT_FOUND]: jsonContent(notFoundSchema, 'User not found'),
		[StatusCode.UNPROCESSABLE_ENTITY]: jsonContent(
			createErrorSchema(idParamsSchema),
			'Invalid id error'
		),
	},
});

// Handler types
export type ListRoute = typeof list;
export type GetOneRoute = typeof getOne;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
