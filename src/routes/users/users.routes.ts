import { createRoute, z } from '@hono/zod-openapi';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { jsonContent } from 'stoker/openapi/helpers';
import { UserSchema, CreateUserSchema, UpdateUserSchema } from '@/lib/types';

const tags = ['Users'];

export const list = createRoute({
	method: 'get',
	path: '/users',
	tags,
	responses: {
		[HttpStatusCodes.OK]: jsonContent(z.array(UserSchema), 'The list of users'),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const getById = createRoute({
	path: '/users/{id}',
	method: 'get',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(UserSchema, 'The user details'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const create = createRoute({
	method: 'post',
	path: '/users',
	tags,
	request: {
		body: jsonContent(CreateUserSchema, 'User data'),
	},
	responses: {
		[HttpStatusCodes.CREATED]: jsonContent(
			UserSchema,
			'User created successfully'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid user data'),
			'Invalid user data'
		),
		[HttpStatusCodes.CONFLICT]: jsonContent(
			createMessageObjectSchema('Email already exists'),
			'Email already exists'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const update = createRoute({
	method: 'patch',
	path: '/users/{id}',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
		body: jsonContent(UpdateUserSchema, 'User update data'),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(UserSchema, 'User updated successfully'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid user data'),
			'Invalid user data'
		),
		[HttpStatusCodes.CONFLICT]: jsonContent(
			createMessageObjectSchema('Email already exists'),
			'Email already exists'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

export const remove = createRoute({
	method: 'delete',
	path: '/users/{id}',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			createMessageObjectSchema('User deleted successfully'),
			'User deleted successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
		[HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
			createMessageObjectSchema('Internal server error'),
			'Internal server error'
		),
	},
});

// Export types for handlers
export type ListRoute = typeof list;
export type GetByIdRoute = typeof getById;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
