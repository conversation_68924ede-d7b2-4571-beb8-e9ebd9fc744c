import { createRoute, z } from '@hono/zod-openapi';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { jsonContent } from 'stoker/openapi/helpers';
import { UserModel } from '@/generated/zod';

const tags = ['Users'];

export const list = createRoute({
	path: '/users',
	method: 'get',
	tags,
	responses: {
		[HttpStatusCodes.OK]: jsonContent(z.array(UserModel), 'The list of users'),
	},
});

export const getById = createRoute({
	path: '/users/{id}',
	method: 'get',
	tags,
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(UserModel, 'The user details'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
	},
});

// Create user route
export const create = createRoute({
	tags,
	method: 'post',
	path: '/users',
	request: {
		body: jsonContent(UserModel, 'User data'),
	},
	responses: {
		[HttpStatusCodes.CREATED]: jsonContent(
			UserModel,
			'User created successfully'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid user data'),
			'Invalid user data'
		),
		[HttpStatusCodes.CONFLICT]: jsonContent(
			createMessageObjectSchema('Email already exists'),
			'Email already exists'
		),
	},
});

// Update user route
export const update = createRoute({
	tags,
	method: 'patch',
	path: '/users/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
		body: jsonContent(UserModel, 'User update data'),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(UserModel, 'User updated successfully'),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid user data'),
			'Invalid user data'
		),
		[HttpStatusCodes.CONFLICT]: jsonContent(
			createMessageObjectSchema('Email already exists'),
			'Email already exists'
		),
	},
});

// Delete user route
export const remove = createRoute({
	tags,
	method: 'delete',
	path: '/users/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			createMessageObjectSchema('User deleted successfully'),
			'User deleted successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
	},
});

// Export types for handlers
export type ListRoute = typeof list;
export type GetByIdRoute = typeof getById;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
