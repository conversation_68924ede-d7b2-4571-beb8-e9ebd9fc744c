import { createRoute, z } from '@hono/zod-openapi';
import * as HttpStatusCodes from 'stoker/http-status-codes';
import { createMessageObjectSchema } from 'stoker/openapi/schemas';
import { jsonContent } from 'stoker/openapi/helpers';

// User schema for responses
const UserSchema = z.object({
	id: z.string(),
	email: z.string().email(),
	name: z.string().nullable(),
	profile: z.object({
		id: z.string(),
		bio: z.string().nullable(),
		userId: z.string(),
	}).nullable(),
});

// User schema with tasks for detailed view
const UserWithTasksSchema = UserSchema.extend({
	tasks: z.array(z.object({
		id: z.string(),
		title: z.string(),
		content: z.string().nullable(),
		done: z.boolean(),
		createdAt: z.string(),
		updatedAt: z.string(),
	})),
});

// Input schemas
const CreateUserSchema = z.object({
	email: z.string().email(),
	name: z.string().optional(),
	bio: z.string().optional(),
});

const UpdateUserSchema = z.object({
	email: z.string().email().optional(),
	name: z.string().optional(),
	bio: z.string().optional(),
});

// List users route
export const list = createRoute({
	tags: ['Users'],
	method: 'get',
	path: '/users',
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			z.array(UserSchema),
			'The list of users'
		),
	},
});

// Get user by ID route
export const getById = createRoute({
	tags: ['Users'],
	method: 'get',
	path: '/users/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			UserWithTasksSchema,
			'The user details'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
	},
});

// Create user route
export const create = createRoute({
	tags: ['Users'],
	method: 'post',
	path: '/users',
	request: {
		body: jsonContent(CreateUserSchema, 'User data'),
	},
	responses: {
		[HttpStatusCodes.CREATED]: jsonContent(
			UserSchema,
			'User created successfully'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid user data'),
			'Invalid user data'
		),
		[HttpStatusCodes.CONFLICT]: jsonContent(
			createMessageObjectSchema('Email already exists'),
			'Email already exists'
		),
	},
});

// Update user route
export const update = createRoute({
	tags: ['Users'],
	method: 'patch',
	path: '/users/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
		body: jsonContent(UpdateUserSchema, 'User update data'),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			UserSchema,
			'User updated successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
		[HttpStatusCodes.BAD_REQUEST]: jsonContent(
			createMessageObjectSchema('Invalid user data'),
			'Invalid user data'
		),
		[HttpStatusCodes.CONFLICT]: jsonContent(
			createMessageObjectSchema('Email already exists'),
			'Email already exists'
		),
	},
});

// Delete user route
export const remove = createRoute({
	tags: ['Users'],
	method: 'delete',
	path: '/users/{id}',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		[HttpStatusCodes.OK]: jsonContent(
			createMessageObjectSchema('User deleted successfully'),
			'User deleted successfully'
		),
		[HttpStatusCodes.NOT_FOUND]: jsonContent(
			createMessageObjectSchema('User not found'),
			'User not found'
		),
	},
});

// Export types for handlers
export type ListRoute = typeof list;
export type GetByIdRoute = typeof getById;
export type CreateRoute = typeof create;
export type UpdateRoute = typeof update;
export type RemoveRoute = typeof remove;
